/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Orbitron', monospace;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* Animated Background */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        #0a0a0a 0%,
        #1a1a2e 25%,
        #16213e 50%,
        #0f3460 75%,
        #0a0a0a 100%);
    animation: bgShift 8s ease-in-out infinite;
}

@keyframes bgShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(2deg); }
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Particle Field */
.particle-field {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #00ffff;
    border-radius: 50%;
    animation: particleFloat 6s linear infinite;
    box-shadow: 0 0 6px #00ffff;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Main Game Arena */
.game-arena {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Header Styling */
.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.neon-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.title-main {
    font-family: 'Orbitron', monospace;
    font-size: 3.5rem;
    font-weight: 900;
    color: #00ffff;
    text-shadow:
        0 0 5px #00ffff,
        0 0 10px #00ffff,
        0 0 15px #00ffff,
        0 0 20px #00ffff;
    animation: neonPulse 2s ease-in-out infinite alternate;
    letter-spacing: 3px;
}

.title-sub {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.2rem;
    font-weight: 400;
    color: #ffffff;
    opacity: 0.8;
    letter-spacing: 2px;
}

@keyframes neonPulse {
    0% {
        text-shadow:
            0 0 5px #00ffff,
            0 0 10px #00ffff,
            0 0 15px #00ffff,
            0 0 20px #00ffff;
    }
    100% {
        text-shadow:
            0 0 2px #00ffff,
            0 0 5px #00ffff,
            0 0 8px #00ffff,
            0 0 12px #00ffff,
            0 0 15px #00ffff;
    }
}

/* Advanced Scoreboard */
.scoreboard-container {
    margin-bottom: 40px;
}

.scoreboard {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 20px;
    padding: 25px;
    backdrop-filter: blur(10px);
    box-shadow:
        0 8px 32px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.player-section, .computer-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.computer-section {
    flex-direction: row-reverse;
}

.player-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.4), transparent);
    animation: avatarPulse 2s ease-in-out infinite;
}

.ai-glow {
    background: radial-gradient(circle, rgba(255, 0, 255, 0.4), transparent);
}

.avatar-icon {
    font-size: 2rem;
    z-index: 1;
    filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.8));
}

@keyframes avatarPulse {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
}

.score-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.player-label {
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    font-weight: 700;
    color: #00ffff;
    letter-spacing: 1px;
}

.score-value {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.vs-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.vs-text {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 900;
    color: #ff0080;
    text-shadow: 0 0 15px rgba(255, 0, 128, 0.8);
    animation: vsGlow 1.5s ease-in-out infinite alternate;
}

@keyframes vsGlow {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

.round-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.round-label {
    font-size: 0.8rem;
    color: #ffffff;
    opacity: 0.7;
}

.round-value {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: #00ffff;
}

/* Battle Arena */
.battle-arena {
    margin: 40px 0;
    perspective: 1000px;
}

.battle-stage {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 50, 100, 0.3));
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-radius: 25px;
    padding: 40px;
    backdrop-filter: blur(15px);
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform-style: preserve-3d;
}

.hand-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    z-index: 2;
}

.hand-display {
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1), transparent);
    border: 3px solid rgba(0, 255, 255, 0.3);
    border-radius: 50%;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transform-style: preserve-3d;
}

.hand-display:hover {
    transform: rotateY(15deg) scale(1.1);
}

.hand-gesture {
    font-size: 4rem;
    filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.8));
    animation: handFloat 3s ease-in-out infinite;
    z-index: 1;
}

@keyframes handFloat {
    0%, 100% { transform: translateY(0) rotateZ(0deg); }
    50% { transform: translateY(-10px) rotateZ(5deg); }
}

.hand-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.3), transparent);
    animation: handGlow 2s ease-in-out infinite alternate;
}

.ai-hand-glow {
    background: radial-gradient(circle, rgba(255, 0, 255, 0.3), transparent);
}

@keyframes handGlow {
    0% { transform: scale(1); opacity: 0.5; }
    100% { transform: scale(1.3); opacity: 0.8; }
}

.hand-label {
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    font-weight: 600;
    color: #00ffff;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* VS Lightning Effect */
.vs-lightning {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 100px;
    z-index: 1;
}

.lightning-bolt {
    position: absolute;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg,
        transparent,
        #00ffff,
        #ffffff,
        #ff0080,
        transparent);
    top: 50%;
    transform: translateY(-50%);
    animation: lightning 2s ease-in-out infinite;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

@keyframes lightning {
    0%, 100% { opacity: 0.3; transform: translateY(-50%) scaleX(0.8); }
    50% { opacity: 1; transform: translateY(-50%) scaleX(1.2); }
}

.energy-field {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse, rgba(0, 255, 255, 0.2), transparent);
    animation: energyPulse 1.5s ease-in-out infinite;
}

@keyframes energyPulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.5); opacity: 0.7; }
}

/* Choice Arena */
.choice-arena {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 40px 0;
    flex-wrap: wrap;
}

.choice-btn {
    position: relative;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(0, 100, 200, 0.2));
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 20px;
    padding: 25px 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    backdrop-filter: blur(10px);
    min-width: 160px;
    transform-style: preserve-3d;
    overflow: hidden;
}

.choice-btn:hover {
    transform: translateY(-10px) rotateX(10deg);
    border-color: rgba(0, 255, 255, 0.8);
    box-shadow:
        0 20px 40px rgba(0, 255, 255, 0.3),
        0 0 30px rgba(0, 255, 255, 0.5);
}

.choice-btn:active {
    transform: translateY(-5px) rotateX(5deg) scale(0.95);
}

.choice-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.choice-btn:hover .btn-glow {
    opacity: 1;
    animation: btnGlowMove 1.5s ease-in-out infinite;
}

@keyframes btnGlowMove {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.btn-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 1;
    position: relative;
}

.choice-icon {
    font-size: 3rem;
    filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.8));
    animation: iconBob 2s ease-in-out infinite;
}

@keyframes iconBob {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.choice-name {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: #00ffff;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.choice-desc {
    font-size: 0.8rem;
    color: #ffffff;
    opacity: 0.7;
    text-align: center;
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

/* Result Display */
.result-container {
    text-align: center;
    margin: 30px 0;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.result-message {
    font-family: 'Rajdhani', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    animation: resultFadeIn 0.5s ease-out;
    line-height: 1.6;
}

@keyframes resultFadeIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.battle-effects {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

/* Control Panel */
.control-panel {
    text-align: center;
    margin-top: 30px;
}

.control-btn {
    position: relative;
    background: linear-gradient(135deg, #ff0080, #8000ff);
    border: none;
    border-radius: 15px;
    padding: 15px 40px;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    text-transform: uppercase;
    letter-spacing: 1px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(255, 0, 128, 0.4);
}

.control-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(255, 0, 128, 0.6);
}

.control-btn:active {
    transform: translateY(-2px) scale(1.02);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.control-btn:hover .btn-shine {
    left: 100%;
}

/* Special Effects */
.win-effect {
    color: #00ff00 !important;
    text-shadow: 0 0 20px rgba(0, 255, 0, 0.8) !important;
    animation: winPulse 0.5s ease-in-out;
}

.lose-effect {
    color: #ff0040 !important;
    text-shadow: 0 0 20px rgba(255, 0, 64, 0.8) !important;
    animation: losePulse 0.5s ease-in-out;
}

.tie-effect {
    color: #ffff00 !important;
    text-shadow: 0 0 20px rgba(255, 255, 0, 0.8) !important;
    animation: tiePulse 0.5s ease-in-out;
}

@keyframes winPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes losePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(0.9); }
}

@keyframes tiePulse {
    0%, 100% { transform: scale(1); }
    25%, 75% { transform: scale(1.05); }
    50% { transform: scale(1.1); }
}

.battle-animation {
    animation: battleShake 0.5s ease-in-out;
}

@keyframes battleShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.choice-selected {
    border-color: #00ff00 !important;
    box-shadow: 0 0 30px rgba(0, 255, 0, 0.8) !important;
    transform: scale(1.1) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .title-main {
        font-size: 2.5rem;
    }

    .title-sub {
        font-size: 1rem;
    }

    .game-arena {
        padding: 15px;
    }

    .scoreboard {
        padding: 20px 15px;
        flex-direction: column;
        gap: 20px;
    }

    .vs-section {
        order: -1;
    }

    .battle-stage {
        padding: 25px 15px;
        flex-direction: column;
        gap: 30px;
    }

    .vs-lightning {
        width: 100px;
        height: 50px;
        transform: translate(-50%, -50%) rotate(90deg);
    }

    .choice-arena {
        gap: 15px;
    }

    .choice-btn {
        min-width: 120px;
        padding: 20px 15px;
    }

    .choice-icon {
        font-size: 2.5rem;
    }

    .choice-name {
        font-size: 1rem;
    }

    .hand-display {
        width: 100px;
        height: 100px;
    }

    .hand-gesture {
        font-size: 3rem;
    }
}

@media (max-width: 480px) {
    .title-main {
        font-size: 2rem;
    }

    .choice-arena {
        flex-direction: column;
        align-items: center;
    }

    .choice-btn {
        width: 200px;
    }

    .scoreboard {
        padding: 15px;
    }

    .score-value {
        font-size: 2rem;
    }
}

/* Loading Animation */
.loading {
    opacity: 0.5;
    pointer-events: none;
}

.loading .hand-gesture {
    animation: loadingSpin 1s linear infinite;
}

@keyframes loadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Game Over Effects */
.game-over {
    animation: gameOverGlow 2s ease-in-out infinite alternate;
}

@keyframes gameOverGlow {
    0% {
        box-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
    }
    100% {
        box-shadow: 0 0 40px rgba(255, 0, 128, 0.8);
    }
}
