body {
    background: #f0f4f8;
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
}
.container {
    max-width: 400px;
    margin: 40px auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    padding: 32px 24px 24px 24px;
    text-align: center;
}
h1 {
    margin-bottom: 16px;
    color: #333;
}
.scoreboard {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    font-size: 1.1em;
}
.choices {
    display: flex;
    justify-content: space-around;
    margin-bottom: 24px;
}
.choice {
    background: #e3eafc;
    border: none;
    border-radius: 8px;
    padding: 16px 20px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.choice:hover {
    background: #b6ccfe;
    transform: scale(1.08);
}
#result-message {
    min-height: 32px;
    font-size: 1.1em;
    margin-bottom: 16px;
    color: #444;
}
#play-again {
    background: #4f8cff;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 1em;
    cursor: pointer;
    margin-top: 8px;
    transition: background 0.2s;
}
#play-again:hover {
    background: #2563eb;
}
