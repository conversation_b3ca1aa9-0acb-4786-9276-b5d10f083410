// script.js for Rock Paper Scissors game

const choices = ["rock", "paper", "scissors"];
const choiceButtons = document.querySelectorAll(".choice");
const playerScoreSpan = document.getElementById("player-score");
const computerScoreSpan = document.getElementById("computer-score");
const roundCountSpan = document.getElementById("round-count");
const resultMessage = document.getElementById("result-message");
const playAgainBtn = document.getElementById("play-again");

let playerScore = 0;
let computerScore = 0;
let round = 1;
const maxRounds = 5;

function getComputerChoice() {
    const idx = Math.floor(Math.random() * 3);
    return choices[idx];
}

function getWinner(player, computer) {
    if (player === computer) return "tie";
    if (
        (player === "rock" && computer === "scissors") ||
        (player === "scissors" && computer === "paper") ||
        (player === "paper" && computer === "rock")
    ) {
        return "player";
    }
    return "computer";
}

function updateUI(player, computer, winner) {
    let msg = `You chose <b>${player}</b>. Computer chose <b>${computer}</b>.<br>`;
    if (winner === "player") {
        msg += "<span style='color:green;'>You win this round!</span>";
    } else if (winner === "computer") {
        msg += "<span style='color:red;'>Computer wins this round!</span>";
    } else {
        msg += "<span>It's a tie!</span>";
    }
    resultMessage.innerHTML = msg;
    playerScoreSpan.textContent = playerScore;
    computerScoreSpan.textContent = computerScore;
    roundCountSpan.textContent = round;
}

function endGame() {
    let finalMsg = "";
    if (playerScore > computerScore) {
        finalMsg = "<b>Congratulations! You Won The Game!</b>";
    } else if (computerScore > playerScore) {
        finalMsg = "<b>Game Over! Computer Wins The Game!</b>";
    } else {
        finalMsg = "<b>It's a Tie Game! Try Again!</b>";
    }
    resultMessage.innerHTML = finalMsg;
    playAgainBtn.style.display = "inline-block";
    choiceButtons.forEach(btn => btn.disabled = true);
}

function handleChoice(e) {
    if (round > maxRounds) return;
    const playerChoice = e.currentTarget.getAttribute("data-choice");
    const computerChoice = getComputerChoice();
    const winner = getWinner(playerChoice, computerChoice);
    if (winner === "player") playerScore++;
    else if (winner === "computer") computerScore++;
    updateUI(playerChoice, computerChoice, winner);
    if (round === maxRounds) {
        setTimeout(endGame, 800);
    }
    round++;
}

function resetGame() {
    playerScore = 0;
    computerScore = 0;
    round = 1;
    playerScoreSpan.textContent = playerScore;
    computerScoreSpan.textContent = computerScore;
    roundCountSpan.textContent = round;
    resultMessage.innerHTML = "";
    playAgainBtn.style.display = "none";
    choiceButtons.forEach(btn => btn.disabled = false);
}

choiceButtons.forEach(btn => btn.addEventListener("click", handleChoice));
playAgainBtn.addEventListener("click", resetGame);
