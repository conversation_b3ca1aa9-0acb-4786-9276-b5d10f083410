<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rock Paper Scissors</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>Rock Paper Scissors</h1>
        <div class="scoreboard">
            <div>Player: <span id="player-score">0</span></div>
            <div>Computer: <span id="computer-score">0</span></div>
            <div>Round: <span id="round-count">1</span>/5</div>
        </div>
        <div class="choices">
            <button class="choice" data-choice="rock">✊ Rock</button>
            <button class="choice" data-choice="paper">✋ Paper</button>
            <button class="choice" data-choice="scissors">✌️ Scissors</button>
        </div>
        <div id="result-message"></div>
        <button id="play-again" style="display:none;">Play Again</button>
    </div>
    <script src="script.js"></script>
</body>
</html>
